# Enhanced Question Edit Endpoint Documentation

## Overview

The enhanced question edit endpoint now supports comprehensive editing of curated task questions including:
- Question text (Nepali and English)
- Answer options (Nepali and English)
- Answer hint
- Correct answer (supports all task types)
- Automatic audio regeneration using existing PiperTTS infrastructure

## Endpoint Details

### **PUT `/curated-tasks/{task_id}/question`**

**Purpose**: Comprehensive editing of question components with automatic audio regeneration

**Authentication**: Admin/Editor roles required

**Path Parameters**:
- `task_id` (string): The ID of the task item to edit

## Enhanced Request Model

### `QuestionEditRequest`

```json
{
  "text": "नेपालको राजधानी कुन हो?",
  "translated_text": "What is the capital of Nepal?",
  "options": {
    "a": "काठमाडौं",
    "b": "पोखरा",
    "c": "चितवन",
    "d": "भक्तपुर"
  },
  "options_en": {
    "a": "Kathmandu",
    "b": "Pokhara",
    "c": "Chitwan",
    "d": "Bhaktapur"
  },
  "answer_hint": "Capital city",
  "correct_answer": {
    "type": "single_choice",
    "value": "a"
  },
  "regenerate_audio": true
}
```

**New Fields**:
- `answer_hint` (optional): Answer hint text stored in `question.answer_hint`
- `correct_answer` (optional): Correct answer data with type validation

## Correct Answer Support

### **Single Choice Tasks**
```json
{
  "correct_answer": {
    "type": "single_choice",
    "value": "a"
  }
}
```
- **Value Type**: String (e.g., "a", "b", "c", "d")
- **Validation**: Must be a string

### **Multiple Choice Tasks**
```json
{
  "correct_answer": {
    "type": "multiple_choice",
    "value": ["a", "c"]
  }
}
```
- **Value Type**: Array of strings (e.g., ["a", "c"])
- **Validation**: Must be a list

### **Image Identification Tasks**
```json
{
  "correct_answer": {
    "type": "image_identification",
    "value": "a"
  }
}
```
- **Value Type**: String (e.g., "a")
- **Validation**: Must be a string

## Enhanced Response Model

### `QuestionEditResponse`

```json
{
  "success": true,
  "data": {
    "task_id": "64f1234567890abcdef67890",
    "updated_fields": ["text", "options", "answer_hint", "correct_answer"],
    "audio_regenerated": true,
    "correct_answer_updated": true,
    "options_metadata": {
      "a": {
        "text": "काठमाडौं",
        "audio_url": "https://minio.nextai.asia/bucket/audio.wav",
        "file_info": {...},
        "cache_id": "काठमाडौं_audio",
        "generated_at": "2024-01-18T10:30:00Z"
      }
    },
    "message": "Question updated successfully. Fields updated: text, options, answer_hint, correct_answer"
  },
  "message": "Question edited successfully"
}
```

**New Response Fields**:
- `correct_answer_updated`: Boolean indicating if correct answer was modified

## Database Updates

The endpoint now updates these additional fields:

### **Question Fields**:
- `question.answer_hint` - Answer hint text

### **Correct Answer Fields**:
- `correct_answer.type` - Answer type (single_choice, multiple_choice, image_identification)
- `correct_answer.value` - Answer value (string or array based on type)

## Usage Examples

### **1. Edit Single Choice Question with Correct Answer**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "text": "नेपालको राजधानी कुन हो?",
  "options": {
    "a": "काठमाडौं",
    "b": "पोखरा",
    "c": "चितवन",
    "d": "भक्तपुर"
  },
  "answer_hint": "Capital city",
  "correct_answer": {
    "type": "single_choice",
    "value": "a"
  }
}
```

### **2. Edit Multiple Choice Question**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "text": "नेपालका कुन शहरहरू पहाडी क्षेत्रमा छन्?",
  "options": {
    "a": "काठमाडौं",
    "b": "पोखरा",
    "c": "जनकपुर",
    "d": "भक्तपुर"
  },
  "correct_answer": {
    "type": "multiple_choice",
    "value": ["a", "b", "d"]
  }
}
```

### **3. Edit Image Identification Question**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "text": "तस्बिरमा के देखिन्छ?",
  "options": {
    "a": "हात्ती",
    "b": "बाघ",
    "c": "गैंडा",
    "d": "हिरण"
  },
  "correct_answer": {
    "type": "image_identification",
    "value": "c"
  }
}
```

### **4. Edit Only Answer Hint**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "answer_hint": "Think about the largest city"
}
```

## Validation Features

### **Type-Specific Validation**:
- **Single Choice**: Validates that `value` is a string
- **Multiple Choice**: Validates that `value` is an array
- **Image Identification**: Validates that `value` is a string

### **Error Responses**:
- **400**: Invalid correct answer type/value combination
- **400**: No fields provided for update
- **404**: Task not found
- **500**: Database or audio generation errors

## Audio Generation

- **Uses Existing Infrastructure**: Leverages `_generate_single_option_audio` from socket service v2
- **PiperTTS Integration**: Automatic audio generation for modified options
- **Error Resilience**: Individual option failures don't stop the entire operation

## Backward Compatibility

- **All existing functionality preserved**
- **New fields are optional**
- **Existing API calls continue to work unchanged**
- **Response format enhanced but maintains core structure**

This enhanced endpoint provides complete control over curated question editing while maintaining consistency with existing system patterns and ensuring robust validation for all task types.
