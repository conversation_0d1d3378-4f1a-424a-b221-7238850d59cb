# Complete Question Edit Endpoint Documentation

## Overview

The fully enhanced question edit endpoint now provides comprehensive editing capabilities for all curated task components with automatic media generation:

- **Question text** (Nepali and English)
- **Answer options** (Nepali and English) with automatic audio generation
- **Answer hint** with automatic media generation for specific task types
- **Correct answer** (supports all task types with validation)
- **Automatic media generation** for hints based on task type

## Endpoint Details

### **PUT `/curated-tasks/{task_id}/question`**

**Purpose**: Complete question editing with automatic media generation

**Authentication**: Admin/Editor roles required

## Enhanced Request Model

### `QuestionEditRequest`

```json
{
  "text": "नेपालको राजधानी कुन हो?",
  "translated_text": "What is the capital of Nepal?",
  "options": {
    "a": "काठमाडौं",
    "b": "पोखरा",
    "c": "चितवन",
    "d": "भक्तपुर"
  },
  "options_en": {
    "a": "Kathmandu",
    "b": "Pokhara",
    "c": "Chitwan",
    "d": "Bhaktapur"
  },
  "answer_hint": "Capital city",
  "correct_answer": {
    "type": "single_choice",
    "value": "a"
  },
  "regenerate_audio": true
}
```

## Automatic Hint Media Generation

### **Image Identification Tasks**
When `answer_hint` is provided for `image_identification` tasks:
- **Automatically generates image** using existing image generation service
- **Stores in**: `question.metadata`
- **Updates**: `metadata._hint_image_ready` and `metadata.hint_image_status`

**Example**:
```json
{
  "answer_hint": "image of bell",
  "regenerate_audio": true
}
```

### **Speak Word Tasks**
When `answer_hint` is provided for `speak_word` tasks:
- **Automatically generates audio** using PiperTTS
- **Stores in**: `question.metadata`
- **Updates**: `metadata._hint_audio_ready` and `metadata.hint_audio_status`

**Example**:
```json
{
  "answer_hint": "घण्टी",
  "regenerate_audio": true
}
```

## Enhanced Response Model

### `QuestionEditResponse`

```json
{
  "success": true,
  "data": {
    "task_id": "64f1234567890abcdef67890",
    "updated_fields": ["text", "options", "answer_hint", "correct_answer"],
    "audio_regenerated": true,
    "correct_answer_updated": true,
    "hint_media_generated": true,
    "hint_media_type": "image",
    "options_metadata": {
      "a": {
        "text": "काठमाडौं",
        "audio_url": "https://minio.nextai.asia/bucket/audio.wav",
        "file_info": {...},
        "cache_id": "काठमाडौं_audio",
        "generated_at": "2024-01-18T10:30:00Z"
      }
    },
    "question_metadata": {
      "url": "https://minio.nextai.asia/bucket/image.jpg",
      "bucket_name": "nepali-app-media",
      "object_name": "path/image.jpg",
      "hint_text": "image of bell",
      "media_type": "image",
      "generated_at": "2024-01-18T10:30:00Z",
      "usage": {...}
    },
    "message": "Question updated successfully. Fields updated: text, options, answer_hint, correct_answer"
  },
  "message": "Question edited successfully"
}
```

**New Response Fields**:
- `hint_media_generated`: Boolean indicating if hint media was generated
- `hint_media_type`: Type of media generated ("image" or "audio")
- `question_metadata`: Complete metadata for generated hint media

## Database Updates

The endpoint now updates these fields:

### **Question Fields**:
- `question.text`
- `question.translated_text`
- `question.options`
- `question.options_en`
- `question.answer_hint`
- `question.options_metadata` (for options audio)
- `question.metadata` (for hint media) ✨ **NEW**

### **Correct Answer Fields**:
- `correct_answer.type`
- `correct_answer.value`

### **Metadata Status Fields**:
- `metadata._hint_image_ready` ✨ **NEW**
- `metadata.hint_image_status` ✨ **NEW**
- `metadata._hint_audio_ready` ✨ **NEW**
- `metadata.hint_audio_status` ✨ **NEW**

## Usage Examples

### **1. Image Identification Task with Hint Image**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "text": "तस्बिरमा के देखिन्छ?",
  "translated_text": "What do you see in the image?",
  "options": {
    "a": "घण्टी",
    "b": "कुकुर",
    "c": "बिरालो",
    "d": "चरा"
  },
  "answer_hint": "image of bell",
  "correct_answer": {
    "type": "image_identification",
    "value": "a"
  }
}
```

**Result**: Automatically generates image for "image of bell" and stores in `question.metadata`

### **2. Speak Word Task with Hint Audio**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "text": "यो शब्द बोल्नुहोस्",
  "translated_text": "Speak this word",
  "answer_hint": "घण्टी",
  "correct_answer": {
    "type": "speak_word",
    "value": "घण्टी"
  }
}
```

**Result**: Automatically generates audio for "घण्टी" and stores in `question.metadata`

### **3. Complete Edit with All Features**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "text": "नेपालको राजधानी कुन हो?",
  "translated_text": "What is the capital of Nepal?",
  "options": {
    "a": "काठमाडौं",
    "b": "पोखरा",
    "c": "चितवन",
    "d": "भक्तपुर"
  },
  "options_en": {
    "a": "Kathmandu",
    "b": "Pokhara",
    "c": "Chitwan",
    "d": "Bhaktapur"
  },
  "answer_hint": "Capital city",
  "correct_answer": {
    "type": "single_choice",
    "value": "a"
  },
  "regenerate_audio": true
}
```

## Media Generation Features

### **Automatic Detection**:
- **Task Type Detection**: Automatically detects task type from existing task data
- **Media Type Selection**: Chooses appropriate media generation based on task type
- **Smart Processing**: Only generates media for supported task types

### **Supported Task Types**:
- **`image_identification`**: Generates images for answer hints
- **`speak_word`**: Generates audio for answer hints
- **Other types**: No hint media generation (gracefully skipped)

### **Error Handling**:
- **Individual Failures**: Media generation failures don't stop the entire operation
- **Graceful Degradation**: Endpoint succeeds even if media generation fails
- **Detailed Logging**: Comprehensive logging for debugging

## Integration Benefits

1. **Unified Editing**: All question components editable in single request
2. **Automatic Media**: No manual media generation required
3. **Type Safety**: Proper validation for all task types
4. **Existing Infrastructure**: Uses proven media generation services
5. **Performance**: Efficient processing with proper error handling

This complete endpoint provides a comprehensive solution for editing curated content questions while automatically handling all media generation requirements based on task types.
