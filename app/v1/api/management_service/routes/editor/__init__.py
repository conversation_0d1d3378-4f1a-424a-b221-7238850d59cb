"""
Editor routes for the Management Service.

This module contains routes for editor functionality including:
- Getting prompts with pagination
- Curated content management and editing
- Editor-related operations
"""

from fastapi import APIRouter
from app.v1.api.management_service.routes.editor.prompts import router as prompts_router
from app.v1.api.management_service.routes.editor.curated_editor import router as curated_editor_router

# Create main editor router
router = APIRouter()

# Include sub-routers
router.include_router(prompts_router, tags=["Editor - Prompts"])
router.include_router(curated_editor_router, tags=["Editor - Curated Content"])
