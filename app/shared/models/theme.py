"""
Theme models for theme management operations.
"""

from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from bson import ObjectId


class ThemeBase(BaseModel):
    """Base theme model with common fields."""
    name: str = Field(..., description="Theme name in Nepali")
    name_en: str = Field(..., description="Theme name in English")
    description: Optional[str] = Field(None, description="Theme description in Nepali")
    description_en: Optional[str] = Field(None, description="Theme description in English")
    category: str = Field(..., description="Theme category (culture, geography, history, etc.)")
    icon: Optional[str] = Field("🎨", description="Theme icon/emoji")
    background_color: str = Field("#4ECDC4", description="Theme background color (hex format)")
    font_color: str = Field("#FFFFFF", description="Theme font color (hex format)")
    is_active: bool = Field(True, description="Whether the theme is active")


class ThemeCreate(ThemeBase):
    """Model for creating a new theme."""
    pass


class ThemeUpdate(BaseModel):
    """Model for updating an existing theme."""
    name: Optional[str] = Field(None, description="Theme name in Nepali")
    name_en: Optional[str] = Field(None, description="Theme name in English")
    description: Optional[str] = Field(None, description="Theme description in Nepali")
    description_en: Optional[str] = Field(None, description="Theme description in English")
    category: Optional[str] = Field(None, description="Theme category")
    icon: Optional[str] = Field(None, description="Theme icon/emoji")
    background_color: Optional[str] = Field(None, description="Theme background color (hex format)")
    font_color: Optional[str] = Field(None, description="Theme font color (hex format)")
    is_active: Optional[bool] = Field(None, description="Whether the theme is active")


class ThemeColorUpdate(BaseModel):
    """Model for updating only theme colors."""
    background_color: Optional[str] = Field(None, description="Theme background color (hex format)")
    font_color: Optional[str] = Field(None, description="Theme font color (hex format)")


class ThemeResponse(ThemeBase):
    """Model for theme response with additional fields."""
    id: str = Field(..., description="Theme ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    created_by: Optional[str] = Field(None, description="User ID who created the theme")

    class Config:
        from_attributes = True


class ThemeQuestionCreate(BaseModel):
    """Model for creating a new theme question."""
    theme_id: str = Field(..., description="Theme ID this question belongs to")
    text: str = Field(..., description="Question text in Nepali")
    text_en: Optional[str] = Field(None, description="Question text in English (optional)")


class ThemeQuestionUpdate(BaseModel):
    """Model for updating an existing theme question."""
    theme_id: Optional[str] = Field(None, description="Theme ID this question belongs to")
    text: Optional[str] = Field(None, description="Question text in Nepali")
    text_en: Optional[str] = Field(None, description="Question text in English")


class ThemeQuestionResponse(BaseModel):
    """Model for theme question response."""
    id: str = Field(..., description="Question ID")
    theme_id: str = Field(..., description="Theme ID this question belongs to")
    text: str = Field(..., description="Question text in Nepali")
    text_en: Optional[str] = Field(None, description="Question text in English")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True