# Question Edit Endpoint Documentation

## Overview

The new question edit endpoint allows comprehensive editing of curated task questions with automatic audio regeneration using PiperTTS. This endpoint provides the ability to edit question text, translated text, options, and options_en while automatically regenerating audio for modified options.

## Endpoint Details

### **PUT `/curated-tasks/{task_id}/question`**

**Purpose**: Edit question text, translated text, options, and options_en with automatic audio regeneration

**Authentication**: Admin/Editor roles required

**Path Parameters**:
- `task_id` (string): The ID of the task item to edit

## Request Model

### `QuestionEditRequest`

```json
{
  "text": "string (optional)",
  "translated_text": "string (optional)", 
  "options": {
    "a": "Option A text",
    "b": "Option B text",
    "c": "Option C text",
    "d": "Option D text"
  },
  "options_en": {
    "a": "Option A English",
    "b": "Option B English", 
    "c": "Option C English",
    "d": "Option D English"
  },
  "regenerate_audio": true
}
```

**Fields**:
- `text` (optional): Question text in Nepali
- `translated_text` (optional): Question text in English
- `options` (optional): Answer options in Nepali as Dict[str, str]
- `options_en` (optional): Answer options in English as Dict[str, str]
- `regenerate_audio` (boolean): Whether to regenerate audio for options (default: true)

## Response Model

### `QuestionEditResponse`

```json
{
  "success": true,
  "data": {
    "task_id": "64f1234567890abcdef67890",
    "updated_fields": ["text", "options"],
    "audio_regenerated": true,
    "options_metadata": {
      "a": {
        "text": "काठमाडौं",
        "audio_url": "https://minio.nextai.asia/bucket/audio.wav",
        "file_info": {
          "url": "https://minio.nextai.asia/bucket/audio.wav",
          "bucket_name": "nepali-app-media",
          "object_name": "path/audio.wav"
        },
        "cache_id": "काठमाडौं_audio",
        "generated_at": "2024-01-18T10:30:00Z"
      }
    },
    "message": "Question updated successfully. Fields updated: text, options"
  },
  "message": "Question edited successfully"
}
```

## Audio Generation Features

### **Automatic Audio Regeneration**

When `regenerate_audio` is `true` and `options` are provided:

1. **PiperTTS Integration**: Uses PiperTTS service at `http://*************:5000/tts`
2. **Audio Parameters**:
   - Gender: `ne-female-400`
   - Speaker: `0`
   - Length Scale: `1.15`
   - Noise Scale: `0.667`
   - Noise Width: `0.8`
   - Sentence Silence: `0.5`

3. **Storage**: Audio files stored in MinIO under `curated_options_audio` folder
4. **Metadata**: Complete options metadata updated with audio URLs and file info

### **Options Metadata Structure**

Each option gets metadata in the following format:
```json
{
  "option_key": {
    "text": "Option text",
    "audio_url": "https://minio.nextai.asia/bucket/audio.wav",
    "file_info": {
      "url": "https://minio.nextai.asia/bucket/audio.wav",
      "bucket_name": "nepali-app-media",
      "object_name": "curated_options_audio/hash.wav"
    },
    "cache_id": "option_text_audio",
    "generated_at": "2024-01-18T10:30:00Z"
  }
}
```

## Usage Examples

### **1. Edit Question Text Only**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "text": "नेपालको राजधानी कुन हो?",
  "translated_text": "What is the capital of Nepal?"
}
```

### **2. Edit Options with Audio Regeneration**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "options": {
    "a": "काठमाडौं",
    "b": "पोखरा", 
    "c": "चितवन",
    "d": "भक्तपुर"
  },
  "options_en": {
    "a": "Kathmandu",
    "b": "Pokhara",
    "c": "Chitwan", 
    "d": "Bhaktapur"
  },
  "regenerate_audio": true
}
```

### **3. Edit All Fields**

```bash
PUT /v1/management/editor/curated-tasks/64f1234567890abcdef67890/question
{
  "text": "नेपालको राजधानी कुन हो?",
  "translated_text": "What is the capital of Nepal?",
  "options": {
    "a": "काठमाडौं",
    "b": "पोखरा",
    "c": "चितवन", 
    "d": "भक्तपुर"
  },
  "options_en": {
    "a": "Kathmandu",
    "b": "Pokhara",
    "c": "Chitwan",
    "d": "Bhaktapur"
  },
  "regenerate_audio": true
}
```

## Database Updates

The endpoint updates the following fields in `curated_content_items` collection:

### **Question Fields**:
- `question.text`
- `question.translated_text`
- `question.options`
- `question.options_en`

### **Audio Metadata**:
- `question.options_metadata`
- `metadata._options_audio_ready`
- `metadata.options_audio_status`

### **Timestamps**:
- `updated_at`

## Error Handling

- **400**: Invalid task ID format or no fields provided for update
- **404**: Task item not found
- **500**: Database errors or audio generation failures

## Audio Generation Resilience

- **Individual Option Failures**: If audio generation fails for one option, others continue processing
- **Error Tracking**: Failed options include error information in metadata
- **Graceful Degradation**: Endpoint succeeds even if some audio generation fails

## Performance Considerations

- **Sequential Processing**: Options are processed one by one to avoid overwhelming PiperTTS service
- **Async Operations**: All audio generation and file storage operations are asynchronous
- **Efficient Storage**: Uses content-based hashing for audio file names to enable caching

## Integration with Existing System

- **Follows Existing Patterns**: Uses same audio generation patterns as socket service
- **MinIO Integration**: Leverages existing async MinIO client for file storage
- **Database Consistency**: Maintains same metadata structure as other audio generation endpoints
- **Authentication**: Uses existing role-based authentication system

This endpoint provides a comprehensive solution for editing curated content questions while maintaining audio synchronization and following established system patterns.
